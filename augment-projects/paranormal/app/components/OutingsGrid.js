'use client';

import { useState } from 'react';
import OutingCard from './OutingCard';
import RegistrationModal from './RegistrationModal';
import GalleryModal from './GalleryModal';
import { outings } from '../data/outings';

export default function OutingsGrid() {
  const [selectedOuting, setSelectedOuting] = useState(null);
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);
  const [showGalleryModal, setShowGalleryModal] = useState(false);
  const [filter, setFilter] = useState('all');

  const handleRegister = (outing) => {
    setSelectedOuting(outing);
    setShowRegistrationModal(true);
  };

  const handleViewGallery = (outing) => {
    setSelectedOuting(outing);
    setShowGalleryModal(true);
  };

  const handleRegistrationConfirm = (outing) => {
    // In a real app, this would update the backend
    console.log('Registration confirmed for:', outing.title);
  };

  const filteredOutings = outings.filter(outing => {
    if (filter === 'all') return true;
    return outing.status === filter;
  });

  const getFilterCount = (status) => {
    if (status === 'all') return outings.length;
    return outings.filter(outing => outing.status === status).length;
  };

  return (
    <section id="outings-section" className="py-16 px-6 bg-gray-950">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-display text-white mb-4 spooky-glow">
            Upcoming & Past Outings
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Discover our collection of nature gatherings, from upcoming adventures 
            to cherished memories from completed outings.
          </p>
        </div>

        {/* Filter Tabs */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          {[
            { key: 'all', label: 'All Outings', count: getFilterCount('all') },
            { key: 'open', label: 'Open for Registration', count: getFilterCount('open') },
            { key: 'selection', label: 'Selection in Progress', count: getFilterCount('selection') },
            { key: 'completed', label: 'Completed', count: getFilterCount('completed') }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setFilter(tab.key)}
              className={`px-4 py-2 rounded-full text-sm font-semibold transition-all ${
                filter === tab.key
                  ? 'bg-amber-600 text-white'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
              }`}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>

        {/* Outings Grid */}
        {filteredOutings.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredOutings.map((outing) => (
              <OutingCard
                key={outing.id}
                outing={outing}
                onRegister={handleRegister}
                onViewGallery={handleViewGallery}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">No outings found</h3>
            <p className="text-gray-400">
              {filter === 'all' 
                ? 'No outings are currently available.'
                : `No outings with status "${filter}" found.`
              }
            </p>
          </div>
        )}

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="bg-gray-900 rounded-xl p-8 border border-gray-800">
            <h3 className="text-2xl font-bold text-white mb-4">
              Ready to Join Our Community?
            </h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Experience the perfect blend of nature's tranquility and captivating storytelling. 
              Our intimate gatherings create lasting memories and meaningful connections.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary px-6 py-3 rounded-lg text-white font-semibold">
                Learn More About Us
              </button>
              <button className="border border-gray-600 text-gray-300 px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors">
                Contact Us
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      <RegistrationModal
        outing={selectedOuting}
        isOpen={showRegistrationModal}
        onClose={() => setShowRegistrationModal(false)}
        onConfirm={handleRegistrationConfirm}
      />

      <GalleryModal
        outing={selectedOuting}
        isOpen={showGalleryModal}
        onClose={() => setShowGalleryModal(false)}
      />
    </section>
  );
}
