'use client';

import Image from 'next/image';
import { getProgressPercentage, getStatusLabel, getStatusColor } from '../data/outings';

export default function OutingCard({ outing, onRegister, onViewGallery }) {
  const progressPercentage = getProgressPercentage(outing.registeredCount, outing.maxCapacity);
  const statusLabel = getStatusLabel(outing.status);
  const statusColor = getStatusColor(outing.status);

  const handleCardClick = () => {
    if (outing.status === 'completed') {
      onViewGallery(outing);
    }
  };

  const handleRegisterClick = (e) => {
    e.stopPropagation();
    onRegister(outing);
  };

  return (
    <div 
      className={`outing-card bg-gray-900 rounded-xl overflow-hidden shadow-lg border border-gray-800 ${
        outing.status === 'completed' ? 'cursor-pointer' : ''
      }`}
      onClick={handleCardClick}
    >
      {/* Participant Counter */}
      <div className="absolute top-4 right-4 z-20 bg-black bg-opacity-70 text-white px-3 py-1 rounded-full text-sm font-semibold">
        {outing.registeredCount}/{outing.maxCapacity}
      </div>

      {/* Image */}
      <div className="relative h-48 overflow-hidden">
        <Image
          src={outing.image}
          alt={outing.title}
          fill
          className="object-cover transition-transform duration-300 hover:scale-105"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent"></div>
        
        {/* Status Badge */}
        <div className="absolute bottom-4 left-4 z-10">
          <span className={`px-3 py-1 rounded-full text-sm font-semibold bg-black bg-opacity-70 ${statusColor}`}>
            {statusLabel}
          </span>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <h3 className="text-xl font-bold text-white mb-2">{outing.title}</h3>
        <p className="text-gray-300 text-sm mb-4 line-clamp-2">{outing.description}</p>
        
        {/* Location and Date */}
        <div className="flex items-center gap-4 mb-4 text-sm text-gray-400">
          <div className="flex items-center gap-1">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
            </svg>
            <span>{outing.location}</span>
          </div>
          <div className="flex items-center gap-1">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
            </svg>
            <span>{new Date(outing.date).toLocaleDateString()}</span>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-gray-400">Registration Progress</span>
            <span className="text-sm font-semibold text-white">{progressPercentage}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div 
              className={`progress-bar h-2 rounded-full transition-all duration-1000 ${
                outing.status === 'completed' ? 'bg-blue-500' :
                outing.status === 'selection' ? 'bg-yellow-500' : 'bg-green-500'
              }`}
              style={{ '--progress-width': `${progressPercentage}%`, width: `${progressPercentage}%` }}
            ></div>
          </div>
        </div>

        {/* Cost and Action */}
        <div className="flex justify-between items-center">
          <div className="text-lg font-bold text-amber-400">
            ${outing.cost}
          </div>
          
          {outing.status === 'open' && (
            <button
              onClick={handleRegisterClick}
              className="btn-primary px-4 py-2 rounded-lg text-white font-semibold text-sm hover:scale-105 transition-transform"
            >
              Express Interest
            </button>
          )}
          
          {outing.status === 'selection' && (
            <span className="text-yellow-400 text-sm font-semibold">
              Selection in Progress
            </span>
          )}
          
          {outing.status === 'completed' && (
            <span className="text-blue-400 text-sm font-semibold cursor-pointer hover:text-blue-300">
              View Gallery →
            </span>
          )}
        </div>
      </div>
    </div>
  );
}
