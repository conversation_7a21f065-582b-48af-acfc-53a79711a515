'use client';

import Image from 'next/image';

export default function HeroSection() {
  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/images/scary-histories.jpg"
          alt="Nature Outings Background"
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 hero-overlay"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center max-w-4xl mx-auto px-6">
        <h1 className="text-6xl md:text-8xl font-display spooky-glow text-amber-100 mb-6">
          Nature Outings
        </h1>
        
        <p className="text-xl md:text-2xl text-amber-200 mb-4 font-light">
          Where Nature Meets Mystery
        </p>
        
        <div className="max-w-2xl mx-auto mb-8">
          <p className="text-lg text-gray-300 leading-relaxed">
            Join our intimate outdoor gatherings that blend the serenity of nature 
            with the thrill of storytelling. Experience campfire tales under starlit 
            skies, forge meaningful connections, and discover the magic that happens 
            when the sun sets and stories come alive.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <button 
            onClick={() => document.getElementById('outings-section').scrollIntoView({ behavior: 'smooth' })}
            className="btn-primary px-8 py-4 rounded-full text-white font-semibold text-lg hover:scale-105 transition-transform"
          >
            Explore Outings
          </button>
          
          <div className="flex items-center gap-2 text-amber-200">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span className="text-sm">Small groups • Authentic experiences • Memorable stories</span>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
        <div className="animate-bounce">
          <svg className="w-6 h-6 text-amber-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </div>
    </section>
  );
}
