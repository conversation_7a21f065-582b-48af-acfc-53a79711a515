'use client';

import { useState } from 'react';
import Image from 'next/image';

export default function GalleryModal({ outing, isOpen, onClose }) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  if (!isOpen || !outing || !outing.gallery) return null;

  const nextImage = () => {
    setCurrentImageIndex((prev) => 
      prev === outing.gallery.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => 
      prev === 0 ? outing.gallery.length - 1 : prev - 1
    );
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="modal-backdrop absolute inset-0" onClick={onClose}></div>
      
      <div className="relative bg-gray-900 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-gray-700">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-700">
          <div>
            <h2 className="text-2xl font-bold text-white">{outing.title}</h2>
            <p className="text-gray-400">{new Date(outing.date).toLocaleDateString()} • {outing.location}</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Description */}
          <div className="mb-6">
            <p className="text-gray-300 mb-4">{outing.description}</p>
            <div className="flex items-center gap-4 text-sm text-gray-400">
              <span className="flex items-center gap-1">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Completed
              </span>
              <span>{outing.registeredCount} participants</span>
            </div>
          </div>

          {/* Gallery */}
          {outing.gallery.length > 0 && (
            <div className="mb-6">
              <h3 className="text-xl font-bold text-white mb-4">Photo Gallery</h3>
              
              {/* Main Image */}
              <div className="relative h-96 mb-4 rounded-lg overflow-hidden">
                <Image
                  src={outing.gallery[currentImageIndex].url}
                  alt={outing.gallery[currentImageIndex].caption}
                  fill
                  className="object-cover"
                />
                
                {/* Navigation Arrows */}
                {outing.gallery.length > 1 && (
                  <>
                    <button
                      onClick={prevImage}
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>
                    <button
                      onClick={nextImage}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </>
                )}

                {/* Image Counter */}
                <div className="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
                  {currentImageIndex + 1} / {outing.gallery.length}
                </div>
              </div>

              {/* Caption */}
              <p className="text-gray-300 text-center mb-4">
                {outing.gallery[currentImageIndex].caption}
              </p>

              {/* Thumbnail Navigation */}
              {outing.gallery.length > 1 && (
                <div className="flex gap-2 justify-center overflow-x-auto pb-2">
                  {outing.gallery.map((item, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`relative w-16 h-16 rounded-lg overflow-hidden flex-shrink-0 border-2 transition-all ${
                        index === currentImageIndex ? 'border-amber-400' : 'border-transparent'
                      }`}
                    >
                      <Image
                        src={item.url}
                        alt={`Thumbnail ${index + 1}`}
                        fill
                        className="object-cover"
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Activities */}
          <div className="mb-6">
            <h3 className="text-xl font-bold text-white mb-3">Activities Enjoyed</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {outing.activities.map((activity, index) => (
                <div key={index} className="flex items-center gap-2 text-gray-300">
                  <svg className="w-4 h-4 text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  {activity}
                </div>
              ))}
            </div>
          </div>

          {/* Testimonials */}
          {outing.testimonials && outing.testimonials.length > 0 && (
            <div>
              <h3 className="text-xl font-bold text-white mb-4">Participant Testimonials</h3>
              <div className="space-y-4">
                {outing.testimonials.map((testimonial, index) => (
                  <div key={index} className="bg-gray-800 rounded-lg p-4">
                    <p className="text-gray-300 mb-2 italic">"{testimonial.text}"</p>
                    <p className="text-amber-400 font-semibold">— {testimonial.name}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
