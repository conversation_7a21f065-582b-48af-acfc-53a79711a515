@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Creepster&family=Nosifer&family=Griffy:wght@400&family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --background: #0a0a0a;
  --foreground: #ededed;
  --accent: #8b5a3c;
  --accent-light: #a67c5a;
  --danger: #dc2626;
  --success: #16a34a;
  --warning: #eab308;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-accent: var(--accent);
  --color-accent-light: var(--accent-light);
  --color-danger: var(--danger);
  --color-success: var(--success);
  --color-warning: var(--warning);
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-display: 'Griffy', cursive;
  --font-spooky: 'Creepster', cursive;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.6;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: var(--accent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-light);
}

/* Progress bar animations */
@keyframes progress-fill {
  from {
    width: 0%;
  }
  to {
    width: var(--progress-width);
  }
}

.progress-bar {
  animation: progress-fill 1s ease-out;
}

/* Modal backdrop */
.modal-backdrop {
  backdrop-filter: blur(4px);
  background: rgba(0, 0, 0, 0.7);
}

/* Card hover effects */
.outing-card {
  transition: all 0.3s ease;
}

.outing-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Spooky text glow effect */
.spooky-glow {
  text-shadow: 0 0 10px rgba(139, 90, 60, 0.5);
}

/* Button styles */
.btn-primary {
  background: linear-gradient(135deg, var(--accent), var(--accent-light));
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(139, 90, 60, 0.3);
}

/* Hero section overlay */
.hero-overlay {
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.6) 50%,
    rgba(139, 90, 60, 0.3) 100%
  );
}
