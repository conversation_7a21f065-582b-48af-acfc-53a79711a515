// Mock data for Nature Outings
export const outings = [
  {
    id: 1,
    title: "Moonlit Forest Tales",
    description: "Join us for an enchanting evening in the ancient oak forest, where shadows dance and stories come alive around our campfire.",
    image: "/images/scary-histories.jpg",
    status: "open", // open, selection, completed
    registeredCount: 12,
    maxCapacity: 20,
    cost: 45,
    date: "2024-02-15",
    location: "Whispering Oaks Forest",
    activities: [
      "Nature walk at sunset",
      "Campfire storytelling",
      "Star gazing",
      "Traditional ghost stories"
    ],
    gallery: []
  },
  {
    id: 2,
    title: "Lakeside Mysteries",
    description: "Experience the mystique of our secluded lake location, perfect for intimate gatherings and spine-tingling tales.",
    image: "/images/picnic.jpg",
    status: "selection",
    registeredCount: 25,
    maxCapacity: 15,
    cost: 50,
    date: "2024-02-22",
    location: "Mirror Lake",
    activities: [
      "Lakeside picnic",
      "Boat stories",
      "Sunset photography",
      "Water spirit legends"
    ],
    gallery: []
  },
  {
    id: 3,
    title: "Mountain Peak Gathering",
    description: "A challenging but rewarding hike to our mountain peak location, where the veil between worlds feels thinnest.",
    image: "/images/activities.jpg",
    status: "open",
    registeredCount: 8,
    maxCapacity: 12,
    cost: 65,
    date: "2024-03-01",
    location: "Eagle's Peak",
    activities: [
      "Mountain hiking",
      "Peak meditation",
      "Altitude storytelling",
      "Ancient mountain legends"
    ],
    gallery: []
  },
  {
    id: 4,
    title: "Autumn Harvest Stories",
    description: "Our most popular completed outing! A magical autumn evening filled with harvest tales and seasonal mysteries.",
    image: "/images/paranormal.jpg",
    status: "completed",
    registeredCount: 18,
    maxCapacity: 18,
    cost: 40,
    date: "2023-10-31",
    location: "Harvest Moon Farm",
    activities: [
      "Pumpkin patch exploration",
      "Harvest feast",
      "Seasonal storytelling",
      "Halloween traditions"
    ],
    gallery: [
      {
        type: "image",
        url: "/images/scary-histories.jpg",
        caption: "Group gathering around the harvest campfire"
      },
      {
        type: "image", 
        url: "/images/activities.jpg",
        caption: "Exploring the pumpkin patch at sunset"
      },
      {
        type: "image",
        url: "/images/picnic.jpg",
        caption: "Sharing stories and seasonal treats"
      }
    ],
    testimonials: [
      {
        name: "Sarah M.",
        text: "An absolutely magical evening! The perfect blend of nature and storytelling."
      },
      {
        name: "Mike R.",
        text: "I've never experienced anything quite like this. The atmosphere was incredible."
      }
    ]
  },
  {
    id: 5,
    title: "Winter Solstice Circle",
    description: "A completed winter gathering that brought warmth to the coldest night of the year.",
    image: "/images/register-modal.jpg",
    status: "completed",
    registeredCount: 15,
    maxCapacity: 15,
    cost: 55,
    date: "2023-12-21",
    location: "Evergreen Grove",
    activities: [
      "Winter nature walk",
      "Solstice ceremony",
      "Hot cocoa by the fire",
      "Winter folklore sharing"
    ],
    gallery: [
      {
        type: "image",
        url: "/images/register-modal.jpg",
        caption: "Cozy winter gathering by the fire"
      },
      {
        type: "image",
        url: "/images/paranormal.jpg",
        caption: "Snow-covered evergreen setting"
      }
    ],
    testimonials: [
      {
        name: "Emma L.",
        text: "The winter setting was breathtaking. Perfect for intimate storytelling."
      },
      {
        name: "David K.",
        text: "A wonderful way to celebrate the solstice with like-minded people."
      }
    ]
  }
];

// Mock user data
export const currentUser = {
  id: 1,
  name: "Alex Thompson",
  email: "<EMAIL>",
  balance: 150,
  registeredOutings: [1, 2] // IDs of outings user has registered for
};

// Helper functions
export const getOutingById = (id) => {
  return outings.find(outing => outing.id === id);
};

export const getOutingsByStatus = (status) => {
  return outings.filter(outing => outing.status === status);
};

export const getProgressPercentage = (registered, max) => {
  return Math.round((registered / max) * 100);
};

export const getStatusLabel = (status) => {
  const labels = {
    open: "Open for Registration",
    selection: "Selection in Progress", 
    completed: "Completed"
  };
  return labels[status] || "Unknown";
};

export const getStatusColor = (status) => {
  const colors = {
    open: "text-green-400",
    selection: "text-yellow-400",
    completed: "text-blue-400"
  };
  return colors[status] || "text-gray-400";
};
