import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: 'swap',
});

export const metadata = {
  title: "Nature Outings - Where Nature Meets Mystery",
  description: "Join our intimate outdoor gatherings that blend the serenity of nature with the thrill of storytelling. Experience campfire tales under starlit skies and forge meaningful connections.",
  keywords: "nature outings, outdoor activities, campfire stories, paranormal, storytelling, group activities",
  authors: [{ name: "Nature Outings" }],
  openGraph: {
    title: "Nature Outings - Where Nature Meets Mystery",
    description: "Join our intimate outdoor gatherings that blend the serenity of nature with the thrill of storytelling.",
    type: "website",
    images: [
      {
        url: "/images/scary-histories.jpg",
        width: 1200,
        height: 630,
        alt: "Nature Outings - Campfire storytelling in nature",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Nature Outings - Where Nature Meets Mystery",
    description: "Join our intimate outdoor gatherings that blend the serenity of nature with the thrill of storytelling.",
    images: ["/images/scary-histories.jpg"],
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.variable} antialiased`}>
        {children}
      </body>
    </html>
  );
}
